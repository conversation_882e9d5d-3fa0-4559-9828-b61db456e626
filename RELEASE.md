# Release Process

This project uses [Intuit Auto](https://intuit.github.io/auto/) for automated semantic versioning and releases.

## How It Works

1. **Pull Requests**: When you create a PR, the Auto bot will automatically label it based on the changes
2. **Merging**: When PRs are merged to `main`, Auto determines the version bump based on labels
3. **Releases**: Auto creates GitHub releases and publishes the CLI package to GitHub Packages

## PR Labels

Auto uses these labels to determine version bumps:

- 🔴 **major** - Breaking changes (e.g., API changes, removed features)
- 🟡 **minor** - New features (e.g., new commands, enhancements)
- 🟢 **patch** - Bug fixes (e.g., fixes, small improvements)
- ⚪ **skip-release** - No version bump (e.g., docs, tests, CI changes)
- 🏠 **internal** - Internal changes that don't affect users
- 📝 **documentation** - Documentation-only changes

## Manual Release

To trigger a manual release:

1. Go to the Actions tab in GitHub
2. Select "Release and Publish" workflow
3. Click "Run workflow" on the main branch

## Local Development

To test Auto locally:

```bash
# Install Auto globally
npm install -g auto

# Check what the next version would be
auto version

# Generate changelog
auto changelog

# Create labels in your repo (one-time setup)
auto create-labels
```

## Package Installation

Once published, users can install the CLI:

```bash
# Install globally
npm install -g @your-org/tempo

# Or run directly
npx @your-org/tempo
```

## Conventional Commits

While not required, using conventional commits helps Auto determine the right labels:

- `feat:` → minor version bump
- `fix:` → patch version bump
- `feat!:` or `BREAKING CHANGE:` → major version bump
- `docs:`, `test:`, `ci:` → no version bump

Example:
```
feat: add new calendar view command

This adds a new --view flag to display calendar in different formats.
```
