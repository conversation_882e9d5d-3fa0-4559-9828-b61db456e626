name: CI

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

jobs:
  lint-test-build:
    name: Lint, Test & Build
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22.17.1'

      - name: Setup pnpm
        uses: pnpm/action-setup@v4

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Run linting
        run: pnpm lint

      - name: Run tests
        run: pnpm test

      - name: Build application
        run: pnpm build

      - name: Check build artifacts
        run: |
          if [ ! -f "apps/cli/dist/cli.js" ]; then
            echo "Build failed: apps/cli/dist/cli.js not found"
            exit 1
          fi
          echo "Build successful: apps/cli/dist/cli.js exists"

  auto-label:
    name: Auto Label PR
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    permissions:
      contents: read
      pull-requests: write

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22.17.1'

      - name: Setup pnpm
        uses: pnpm/action-setup@v4

      - name: Setup Auto
        run: |
          pnpm install -g auto@11.0.4
          auto --version

      - name: Label PR
        run: auto label --pr ${{ github.event.pull_request.number }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
