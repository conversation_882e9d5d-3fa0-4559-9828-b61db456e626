{"name": "tempo", "version": "0.0.1", "license": "Apache-2.0", "bin": "dist/cli.js", "type": "module", "engines": {"node": "22.17.1", "pnpm": "10.13.1"}, "scripts": {"build": "turbo build", "dev": "turbo dev", "lint": "pnpm exec biome lint", "lint:fix": "pnpm exec biome check --write", "prepare": "husky", "clean": "turbo clean && git clean -xdf .cache .turbo dist node_modules", "test": "turbo test"}, "files": ["dist"], "devDependencies": {"@tsconfig/strictest": "catalog:", "@biomejs/biome": "catalog:", "husky": "catalog:", "lint-staged": "catalog:", "turbo": "catalog:", "typescript": "catalog:", "ultracite": "catalog:"}, "packageManager": "pnpm@10.13.1+sha512.37ebf1a5c7a30d5fabe0c5df44ee8da4c965ca0c5af3dbab28c3a1681b70a256218d05c81c9c0dcf767ef6b8551eb5b960042b9ed4300c59242336377e01cfad", "lint-staged": {"*.{js,jsx,ts,tsx,json,jsonc,css,scss}": ["pnpm exec biome check --write --no-errors-on-unmatched"]}, "repository": "chrismeller/tempo-cli", "author": "pulsar[bot] <pulsar[bot]@users.noreply.github.com>", "auto": {"plugins": ["npm", "all-contributors", "conventional-commits", "first-time-contributor", "released"], "onlyPublishWithReleaseLabel": true}}