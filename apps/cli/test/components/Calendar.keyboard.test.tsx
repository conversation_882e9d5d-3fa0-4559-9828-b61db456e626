import { render } from 'ink-testing-library';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import Calendar from '../../src/calendar.js';

// Mock the useInput hook to capture keyboard handlers
let keyboardHandler: ((input: string, key: unknown) => void) | null = null;

vi.mock('ink', async () => {
  const actual = await vi.importActual('ink');
  return {
    ...actual,
    useInput: vi.fn((handler) => {
      keyboardHandler = handler;
    }),
  };
});

describe('Calendar Keyboard Navigation', () => {
  const mockDayData = {
    1: { entered: 8, expected: 8 },
    15: { entered: 6, expected: 8 },
    31: { entered: 8, expected: 8 },
  };

  const mockOnMonthChange = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    keyboardHandler = null;
  });

  it('should register keyboard input handler', () => {
    render(
      <Calendar
        dayData={mockDayData}
        month={7}
        onMonthChange={mockOnMonthChange}
        year={2025}
      />
    );

    expect(keyboardHandler).toBeDefined();
  });

  it('should handle Page Up for previous month navigation', () => {
    render(
      <Calendar
        dayData={mockDayData}
        month={7}
        onMonthChange={mockOnMonthChange}
        year={2025}
      />
    );

    // Simulate Page Up key press
    if (keyboardHandler) {
      keyboardHandler('', { pageUp: true });
    }

    expect(mockOnMonthChange).toHaveBeenCalledWith(6, 2025); // June 2025
  });

  it('should handle Page Down for next month navigation', () => {
    render(
      <Calendar
        dayData={mockDayData}
        month={7}
        onMonthChange={mockOnMonthChange}
        year={2025}
      />
    );

    // Simulate Page Down key press
    if (keyboardHandler) {
      keyboardHandler('', { pageDown: true });
    }

    expect(mockOnMonthChange).toHaveBeenCalledWith(8, 2025); // August 2025
  });

  it('should handle year boundary navigation with Page Up', () => {
    render(
      <Calendar
        dayData={mockDayData}
        month={1}
        onMonthChange={mockOnMonthChange}
        year={2025}
      />
    );

    // Simulate Page Up key press from January
    if (keyboardHandler) {
      keyboardHandler('', { pageUp: true });
    }

    expect(mockOnMonthChange).toHaveBeenCalledWith(12, 2024); // December 2024
  });

  it('should handle year boundary navigation with Page Down', () => {
    render(
      <Calendar
        dayData={mockDayData}
        month={12}
        onMonthChange={mockOnMonthChange}
        year={2024}
      />
    );

    // Simulate Page Down key press from December
    if (keyboardHandler) {
      keyboardHandler('', { pageDown: true });
    }

    expect(mockOnMonthChange).toHaveBeenCalledWith(1, 2025); // January 2025
  });

  it('should handle arrow key navigation when no day is selected', () => {
    render(
      <Calendar
        dayData={mockDayData}
        month={7}
        onMonthChange={mockOnMonthChange}
        year={2025}
      />
    );

    // Simulate arrow key press when no day is selected
    if (keyboardHandler) {
      keyboardHandler('', { leftArrow: true });
    }

    // Should not crash and should handle gracefully
    expect(mockOnMonthChange).not.toHaveBeenCalled();
  });

  it('should work without onMonthChange callback', () => {
    render(<Calendar dayData={mockDayData} month={7} year={2025} />);

    // Simulate Page Up key press without callback
    if (keyboardHandler) {
      keyboardHandler('', { pageUp: true });
    }

    // Should not crash when no callback is provided
    expect(mockOnMonthChange).not.toHaveBeenCalled();
  });

  it('should handle multiple key presses', () => {
    render(
      <Calendar
        dayData={mockDayData}
        month={7}
        onMonthChange={mockOnMonthChange}
        year={2025}
      />
    );

    // Simulate multiple key presses
    if (keyboardHandler) {
      keyboardHandler('', { pageUp: true });
      keyboardHandler('', { pageDown: true });
      keyboardHandler('', { pageDown: true });
    }

    // Should call the month change handler for each key press
    expect(mockOnMonthChange).toHaveBeenCalledTimes(3);

    // First call should be Page Up (July -> June)
    expect(mockOnMonthChange).toHaveBeenNthCalledWith(1, 6, 2025);

    // Subsequent calls depend on component state, but should be valid month/year combinations
    expect(mockOnMonthChange).toHaveBeenNthCalledWith(
      2,
      expect.any(Number),
      2025
    );
    expect(mockOnMonthChange).toHaveBeenNthCalledWith(
      3,
      expect.any(Number),
      2025
    );
  });

  it('should ignore non-navigation keys', () => {
    render(
      <Calendar
        dayData={mockDayData}
        month={7}
        onMonthChange={mockOnMonthChange}
        year={2025}
      />
    );

    // Simulate non-navigation key presses
    if (keyboardHandler) {
      keyboardHandler('a', {});
      keyboardHandler('1', {});
      keyboardHandler('', { enter: true });
      keyboardHandler('', { escape: true });
    }

    // Should not trigger month changes
    expect(mockOnMonthChange).not.toHaveBeenCalled();
  });
});
