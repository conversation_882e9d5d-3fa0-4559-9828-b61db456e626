import { render } from 'ink-testing-library';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import App from '../../src/app.js';

// Mock the useStdout hook
vi.mock('ink', async () => {
  const actual = await vi.importActual('ink');
  return {
    ...actual,
    useStdout: () => ({
      stdout: {
        columns: 80,
        rows: 24,
      },
    }),
  };
});

// Regex patterns for testing
const hourPattern = /\d+h\/\d+h/;
const weekendPattern = /0h\/0h/;
const weekdayPattern = /\d+h\/8h/;

describe('App Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render without crashing', () => {
    const { lastFrame } = render(<App />);
    expect(lastFrame()).toBeDefined();
  });

  it('should display terminal dimensions', () => {
    const { lastFrame } = render(<App />);
    const output = lastFrame();
    expect(output).toContain('80x24');
  });

  it('should render the Calendar component', () => {
    const { lastFrame } = render(<App />);
    const output = lastFrame();

    // Check for calendar header with current month/year
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    expect(output).toContain(currentYear.toString());
  });

  it('should generate day data for the current month', () => {
    const { lastFrame } = render(<App />);
    const output = lastFrame();

    // Check that the calendar shows hour data (e.g., "8h/8h" for weekdays)
    expect(output).toMatch(hourPattern);
  });

  it('should handle month changes', () => {
    const { lastFrame } = render(<App />);

    // Initial render
    const initialOutput = lastFrame();
    expect(initialOutput).toBeDefined();

    // The component should handle month changes through the Calendar component
    // This is tested more thoroughly in Calendar component tests
    expect(initialOutput).toContain('h/');
  });

  it('should show weekend days with 0 hours', () => {
    const { lastFrame } = render(<App />);
    const output = lastFrame();

    // Weekend days should show 0h/0h
    expect(output).toMatch(weekendPattern);
  });

  it('should show weekdays with 8 expected hours', () => {
    const { lastFrame } = render(<App />);
    const output = lastFrame();

    // Weekdays should show some hours out of 8 expected
    expect(output).toMatch(weekdayPattern);
  });
});
