{"name": "cli", "version": "0.0.1", "private": true, "license": "Apache-2.0", "bin": "dist/cli.js", "type": "module", "scripts": {"build": "tsc", "dev": "tsc --watch", "test": "NODE_ENV=test vitest run", "test:coverage": "NODE_ENV=test vitest run --coverage", "start": "node dist/cli.js", "clean": "git clean -xdf .cache .turbo dist node_modules"}, "files": ["dist"], "dependencies": {"@inkjs/ui": "catalog:cli", "@repo/testing": "workspace:*", "chalk": "catalog:cli", "ink": "catalog:cli", "meow": "catalog:cli", "react": "catalog:react"}, "devDependencies": {"@repo/typescript-config": "workspace:*", "@types/node": "catalog:", "@types/react": "catalog:react", "ava": "catalog:", "ink-testing-library": "catalog:cli", "ts-node": "catalog:", "typescript": "catalog:", "vitest": "catalog:testing", "@vitest/coverage-v8": "catalog:testing"}, "ava": {"extensions": {"ts": "module", "tsx": "module"}, "nodeArguments": ["--loader=ts-node/esm"]}}