{"plugins": ["git-tag", "all-contributors", "first-time-contributor", "released"], "owner": "chrismeller", "repo": "tempo-cli", "name": "Tempo CLI", "email": "pulsar[bot]@users.noreply.github.com", "labels": [{"name": "major", "changelogTitle": "💥 Breaking Change", "description": "Increment the major version when merged", "releaseType": "major", "color": "C5000B"}, {"name": "minor", "changelogTitle": "🚀 Enhancement", "description": "Increment the minor version when merged", "releaseType": "minor", "color": "F1A60E"}, {"name": "patch", "changelogTitle": "🐛 Bug Fix", "description": "Increment the patch version when merged", "releaseType": "patch", "color": "870048"}, {"name": "skip-release", "description": "Preserve the current version when merged", "releaseType": "skip", "color": "bf5416"}, {"name": "release", "description": "Create a release when this pr is merged", "releaseType": "release", "color": "007f70"}, {"name": "internal", "changelogTitle": "🏠 Internal", "description": "Changes only affect the internal API", "releaseType": "none", "color": "696969"}, {"name": "documentation", "changelogTitle": "📝 Documentation", "description": "Changes only affect the documentation", "releaseType": "none", "color": "cfd3d7"}], "baseBranch": "main", "prereleaseBranches": ["develop", "beta", "alpha"], "versionBranches": true}